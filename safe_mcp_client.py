#!/usr/bin/env python3
"""
Safe MCP Client wrapper that handles async context management issues.

This wrapper provides a more robust interface to MCP clients that isolates
async context management errors and provides graceful fallbacks.
"""

import asyncio
import logging
from typing import Optional, Any, Dict
from contextlib import asynccontextmanager

# Configure logging
logger = logging.getLogger(__name__)


class SafeMCPClient:
    """A wrapper around MCP clients that handles async context issues safely."""
    
    def __init__(self, client_class, debug_callback=None):
        self.client_class = client_class
        self.debug_callback = debug_callback or (lambda level, msg, data=None: None)
        self.client = None
        self.connected = False
        
    async def connect(self, server_url: str, timeout: float = 10.0) -> bool:
        """Connect to MCP server with isolated async context."""
        try:
            # Create client in isolated context
            self.client = self.client_class(debug_callback=self.debug_callback)
            
            # Use a separate event loop task to isolate async context
            async def connect_task():
                return await self.client.connect_to_server(server_url)
            
            # Run connection in isolated task with timeout
            success = await asyncio.wait_for(connect_task(), timeout=timeout)
            self.connected = success
            return success
            
        except asyncio.TimeoutError:
            logger.warning(f"Connection timeout to {server_url}")
            self.connected = False
            return False
        except Exception as e:
            logger.warning(f"Connection error to {server_url}: {e}")
            self.connected = False
            return False
    
    async def call_tool(self, tool_name: str, tool_input: Any, tool_call_id: str = "safe_call") -> Optional[Any]:
        """Call a tool with error isolation."""
        if not self.connected or not self.client:
            logger.error("Client not connected")
            return None
            
        try:
            # Isolate tool call in separate task
            async def tool_task():
                return await self.client.call_tool(tool_name, tool_input, tool_call_id)
            
            result = await asyncio.wait_for(tool_task(), timeout=30.0)
            return result
            
        except asyncio.TimeoutError:
            logger.warning(f"Tool call timeout: {tool_name}")
            return None
        except Exception as e:
            logger.warning(f"Tool call error: {tool_name}: {e}")
            return None
    
    async def cleanup(self, timeout: float = 3.0):
        """Safe cleanup with timeout and error isolation."""
        if not self.client:
            return
            
        self.connected = False
        
        try:
            # Isolate cleanup in separate task
            async def cleanup_task():
                if hasattr(self.client, 'cleanup'):
                    await self.client.cleanup()
            
            await asyncio.wait_for(cleanup_task(), timeout=timeout)
            logger.info("Safe cleanup completed")
            
        except asyncio.TimeoutError:
            logger.info("Safe cleanup timeout (acceptable)")
        except Exception as e:
            logger.info(f"Safe cleanup warning: {e}")
        finally:
            self.client = None
    
    @property
    def available_tools(self):
        """Get available tools safely."""
        if self.client and hasattr(self.client, 'available_tools'):
            return self.client.available_tools
        return []


@asynccontextmanager
async def safe_mcp_connection(client_class, server_url: str, debug_callback=None):
    """Context manager for safe MCP connections."""
    client = SafeMCPClient(client_class, debug_callback)
    
    try:
        success = await client.connect(server_url)
        if success:
            yield client
        else:
            yield None
    finally:
        await client.cleanup()


async def test_safe_client():
    """Test the safe client wrapper."""
    import sys
    import os
    
    # Add the gaia path
    sys.path.append(os.path.join(os.path.dirname(__file__), 'gaia'))
    
    try:
        from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib
    except ImportError as e:
        print(f"Error importing MCP client library: {e}")
        return
    
    print("🛡️  Testing Safe MCP Client")
    print("=" * 30)
    
    # Test with context manager
    async with safe_mcp_connection(MCPClientLib, "http://localhost:9000/mcp") as client:
        if client:
            print("✅ Safe connection established")
            
            # Test tool call
            result = await client.call_tool("server_status", {})
            if result and result.success:
                print("✅ Safe tool call successful")
            else:
                print("❌ Safe tool call failed")
        else:
            print("❌ Safe connection failed")
    
    print("✅ Safe client test completed without async errors")


if __name__ == "__main__":
    asyncio.run(test_safe_client())
