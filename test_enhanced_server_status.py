#!/usr/bin/env python3
"""
Test script to check the enhanced MCP server status and available tools.
"""

import asyncio
import sys
import os

# Add the gaia path to import MCP client libraries
sys.path.append(os.path.join(os.path.dirname(__file__), 'gaia'))

try:
    from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib
except ImportError as e:
    print(f"Error importing MCP client library: {e}")
    sys.exit(1)


async def test_server_status():
    """Test the enhanced MCP server status and tools."""
    
    # Create client
    client = MCPClientLib(debug_callback=lambda level, msg, data=None: print(f"[{level.upper()}] {msg}"))
    
    try:
        # Connect to the enhanced server
        print("Connecting to enhanced MCP server at http://localhost:9000/mcp...")
        success = await client.connect_to_server("http://localhost:9000/mcp")
        
        if not success:
            print("❌ Failed to connect to enhanced MCP server")
            return
        
        print("✅ Connected successfully!")
        
        # List available tools
        print(f"\n📋 Available tools ({len(client.available_tools)}):")
        for i, tool in enumerate(client.available_tools, 1):
            print(f"  {i}. {tool['name']}: {tool.get('description', 'No description')}")
        
        # Test server_status tool
        print("\n🔍 Testing server_status tool...")
        try:
            result = await client.call_tool("server_status", {}, "test_status")
            if result.success:
                print("✅ Server status:")
                print(result.content)
            else:
                print(f"❌ Server status failed: {result.error}")
        except Exception as e:
            print(f"❌ Server status error: {e}")
        
        # Test list_all_tools
        print("\n🔍 Testing list_all_tools...")
        try:
            result = await client.call_tool("list_all_tools", {}, "test_tools")
            if result.success:
                print("✅ All tools:")
                print(result.content)
            else:
                print(f"❌ List tools failed: {result.error}")
        except Exception as e:
            print(f"❌ List tools error: {e}")
        
        # Test third_party_health
        print("\n🔍 Testing third_party_health...")
        try:
            result = await client.call_tool("third_party_health", {}, "test_health")
            if result.success:
                print("✅ Third-party health:")
                print(result.content)
            else:
                print(f"❌ Third-party health failed: {result.error}")
        except Exception as e:
            print(f"❌ Third-party health error: {e}")
        
        # Check for Firecrawl tools
        firecrawl_tools = [tool for tool in client.available_tools if 'firecrawl' in tool['name'].lower()]
        if firecrawl_tools:
            print(f"\n🔥 Found {len(firecrawl_tools)} Firecrawl tools:")
            for tool in firecrawl_tools:
                print(f"  - {tool['name']}: {tool.get('description', 'No description')}")
            
            # Test a simple Firecrawl tool call
            firecrawl_tool = firecrawl_tools[0]
            print(f"\n🔍 Testing {firecrawl_tool['name']} tool...")
            try:
                # Use a simple test URL
                test_input = {"url": "https://example.com"}
                result = await client.call_tool(firecrawl_tool['name'], test_input, "test_firecrawl")
                if result.success:
                    print("✅ Firecrawl tool test successful!")
                    print(f"Content preview: {str(result.content)[:200]}...")
                else:
                    print(f"❌ Firecrawl tool test failed: {result.error}")
            except Exception as e:
                print(f"❌ Firecrawl tool test error: {e}")
        else:
            print("\n⚠️  No Firecrawl tools found")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        try:
            await client.cleanup()
            print("\n🧹 Client cleanup completed")
        except Exception as e:
            print(f"⚠️  Cleanup warning: {e}")


if __name__ == "__main__":
    print("🚀 Testing Enhanced MCP Server")
    print("=" * 40)
    asyncio.run(test_server_status())
