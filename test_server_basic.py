#!/usr/bin/env python3
"""
Basic test to check if the enhanced MCP server is responding.
"""

import asyncio
import aiohttp
import json


async def test_basic_connection():
    """Test basic HTTP connection to the MCP server."""
    
    server_url = "http://localhost:9000"
    
    async with aiohttp.ClientSession() as session:
        try:
            # Test basic server response
            print(f"Testing basic connection to {server_url}...")
            async with session.get(f"{server_url}/") as response:
                print(f"Status: {response.status}")
                if response.status == 200:
                    text = await response.text()
                    print(f"Response: {text[:200]}...")
                else:
                    print(f"Error response: {response.status}")
        
        except Exception as e:
            print(f"Basic connection failed: {e}")
        
        try:
            # Test MCP endpoint
            print(f"\nTesting MCP endpoint at {server_url}/mcp...")
            async with session.get(f"{server_url}/mcp") as response:
                print(f"MCP Status: {response.status}")
                if response.status == 200:
                    text = await response.text()
                    print(f"MCP Response: {text[:200]}...")
                else:
                    print(f"MCP Error response: {response.status}")
        
        except Exception as e:
            print(f"MCP endpoint test failed: {e}")
        
        try:
            # Test health endpoint if it exists
            print(f"\nTesting health endpoint at {server_url}/health...")
            async with session.get(f"{server_url}/health") as response:
                print(f"Health Status: {response.status}")
                if response.status == 200:
                    text = await response.text()
                    print(f"Health Response: {text}")
                else:
                    print(f"Health endpoint not available: {response.status}")
        
        except Exception as e:
            print(f"Health endpoint test failed: {e}")


if __name__ == "__main__":
    print("🔍 Basic Server Connection Test")
    print("=" * 35)
    asyncio.run(test_basic_connection())
