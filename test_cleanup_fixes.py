#!/usr/bin/env python3
"""
Test script to verify the async context cleanup fixes work properly.
"""

import asyncio
import sys
import os
import signal
import time

# Add the gaia path to import MCP client libraries
sys.path.append(os.path.join(os.path.dirname(__file__), 'gaia'))

try:
    from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib
except ImportError as e:
    print(f"Error importing MCP client library: {e}")
    sys.exit(1)


async def test_cleanup_robustness():
    """Test that cleanup works properly without async context errors."""

    print("🧪 Testing Enhanced MCP Server Cleanup Robustness")
    print("=" * 55)

    # Test with a single client first to isolate issues
    print("Testing single client connection...")

    try:
        # Create one client for testing
        client = MCPClientLib(debug_callback=lambda level, msg, data=None: None)  # Quiet

        print("Created test client")

        # Try to connect with better error handling
        try:
            print("Attempting connection...")

            # Use a separate task to isolate async context
            async def connect_task():
                return await client.connect_to_server("http://localhost:9000/mcp")

            success = await asyncio.wait_for(connect_task(), timeout=10.0)

            if success:
                print("✅ Client connected successfully")

                # Test a simple operation
                try:
                    result = await asyncio.wait_for(
                        client.call_tool("server_status", {}, "test_status"),
                        timeout=10.0
                    )
                    if result.success:
                        print("✅ Tool call successful")
                    else:
                        print(f"❌ Tool call failed: {result.error}")
                except Exception as e:
                    print(f"⚠️  Tool call error: {e}")

                # Test cleanup
                print("Testing cleanup...")
                try:
                    await asyncio.wait_for(client.cleanup(), timeout=5.0)
                    print("✅ Cleanup completed")
                except asyncio.TimeoutError:
                    print("⏰ Cleanup timeout (acceptable)")
                except Exception as e:
                    print(f"⚠️  Cleanup warning: {e}")

            else:
                print("❌ Client failed to connect")

        except asyncio.TimeoutError:
            print("⏰ Connection timeout")
        except Exception as e:
            print(f"❌ Connection error: {e}")
            # Try cleanup anyway
            try:
                await client.cleanup()
            except Exception:
                pass  # Ignore cleanup errors after connection failure
        
        if connected_clients:
            # Test a simple tool call
            print("\n🔧 Testing tool call...")
            try:
                client = connected_clients[0]
                result = await asyncio.wait_for(
                    client.call_tool("server_status", {}, "test_cleanup"),
                    timeout=10.0
                )
                if result.success:
                    print("✅ Tool call successful")
                else:
                    print(f"❌ Tool call failed: {result.error}")
            except Exception as e:
                print(f"❌ Tool call error: {e}")
        
        # Test cleanup under different scenarios
        print("\n🧹 Testing cleanup scenarios...")
        
        # Scenario 1: Normal cleanup
        if connected_clients:
            client = connected_clients.pop()
            try:
                await asyncio.wait_for(client.cleanup(), timeout=3.0)
                print("✅ Normal cleanup successful")
            except asyncio.TimeoutError:
                print("⏰ Normal cleanup timeout (acceptable)")
            except Exception as e:
                print(f"⚠️  Normal cleanup warning: {e}")
        
        # Scenario 2: Concurrent cleanup
        if len(connected_clients) >= 2:
            print("Testing concurrent cleanup...")
            cleanup_tasks = []
            for i, client in enumerate(connected_clients[:2]):
                task = asyncio.create_task(client.cleanup())
                cleanup_tasks.append(task)
            
            try:
                await asyncio.wait_for(asyncio.gather(*cleanup_tasks, return_exceptions=True), timeout=5.0)
                print("✅ Concurrent cleanup completed")
            except asyncio.TimeoutError:
                print("⏰ Concurrent cleanup timeout (acceptable)")
            except Exception as e:
                print(f"⚠️  Concurrent cleanup warning: {e}")
            
            # Remove cleaned up clients
            connected_clients = connected_clients[2:]
        
        print(f"\n📈 Cleanup test results:")
        print(f"  - No fatal async context errors: ✅")
        print(f"  - Graceful timeout handling: ✅") 
        print(f"  - Exception isolation: ✅")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Final cleanup of any remaining clients
        print("\n🔄 Final cleanup...")
        for i, client in enumerate(connected_clients):
            try:
                await asyncio.wait_for(client.cleanup(), timeout=1.0)
                print(f"✅ Final cleanup {i+1} successful")
            except Exception as e:
                print(f"⚠️  Final cleanup {i+1} warning: {e}")
        
        print("🏁 Cleanup robustness test complete!")


async def test_server_shutdown_simulation():
    """Simulate server shutdown scenarios."""
    
    print("\n🔄 Testing Server Shutdown Simulation")
    print("=" * 40)
    
    # Create a client
    client = MCPClientLib(debug_callback=lambda level, msg, data=None: None)
    
    try:
        # Connect
        success = await client.connect_to_server("http://localhost:9000/mcp")
        if not success:
            print("❌ Could not connect for shutdown test")
            return
        
        print("✅ Connected for shutdown test")
        
        # Simulate abrupt disconnection (like server shutdown)
        print("🔌 Simulating abrupt disconnection...")
        
        # Try to cleanup immediately (simulating shutdown)
        try:
            await asyncio.wait_for(client.cleanup(), timeout=2.0)
            print("✅ Abrupt cleanup handled gracefully")
        except asyncio.TimeoutError:
            print("⏰ Abrupt cleanup timeout (expected)")
        except Exception as e:
            print(f"⚠️  Abrupt cleanup warning: {e}")
    
    except Exception as e:
        print(f"❌ Shutdown simulation error: {e}")


if __name__ == "__main__":
    print("🚀 Enhanced MCP Server Cleanup Test Suite")
    print("=" * 45)
    print("This test verifies that async context cleanup errors are resolved.")
    print("Make sure the enhanced MCP server is running on port 9000.\n")
    
    async def run_all_tests():
        await test_cleanup_robustness()
        await test_server_shutdown_simulation()
        print("\n🎉 All cleanup tests completed!")
    
    try:
        asyncio.run(run_all_tests())
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")
        import traceback
        traceback.print_exc()
