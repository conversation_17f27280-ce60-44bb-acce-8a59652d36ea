# Multi-MCP HTTP Server Architecture

This document explains how the Enhanced MCP HTTP Server implements a sophisticated multi-MCP architecture that allows a single client to access tools from multiple MCP servers seamlessly.

## 🏗️ Architecture Overview

```mermaid
graph TB
    Client[Chat Client/chat_term] --> EnhancedServer[Enhanced MCP Server :9000]
    
    subgraph "Enhanced MCP Server"
        LocalTools[Local Tools<br/>- echostring<br/>- long_task<br/>- server_status]
        DelegatedTools[Delegated Tools<br/>- web_firecrawl_scrape<br/>- web_firecrawl_map<br/>- etc.]
        MultiClient[MultiMCPClient]
    end
    
    EnhancedServer --> MultiClient
    MultiClient --> FirecrawlMCP[Firecrawl MCP Server<br/>:hosted]
    MultiClient --> OtherMCP[Other MCP Servers<br/>:various ports]
    
    subgraph "Tool Registration"
        ToolMapping[Tool Name Mapping<br/>firecrawl_scrape → web__firecrawl_scrape]
        ToolDelegation[Tool Delegation Logic]
    end
```

## 🔄 How It Works: Step by Step

### 1. Server Initialization

The Enhanced MCP Server combines local tools with third-party MCP server delegation:

```python
class EnhancedMCPServer:
    def __init__(self, config_file: str = "server_config.json"):
        self.config_file = config_file
        self.mcp = FastMCP("gaia_enhanced_mcp_server")  # Main MCP server
        self.third_party_client = MultiMCPClient()     # Client for other servers
        self.third_party_tools: Dict[str, Dict[str, Any]] = {}  # Tool mapping
        
        # Register local tools first
        self._register_local_tools()
```

### 2. Third-Party Server Connection

The server reads configuration and connects to third-party MCP servers:

```python
async def _spawn_and_connect_server(self, server_id: str, command: str,
                                  args: List[str], env: Dict[str, str],
                                  namespace: str) -> bool:
    # Connect to third-party MCP server
    success = await self.third_party_client.add_server(
        server_id=server_id,
        server_url=server_url,
        protocol='sse',
        description='Firecrawl MCP server'
    )
    
    if success:
        await self._register_third_party_tools(server_id, namespace)
```

### 3. Tool Registration & Namespacing

Tools from third-party servers are registered with namespace prefixes:

```python
async def _register_third_party_tools(self, server_id: str, namespace: str):
    """Register tools from a third-party server as delegated tools."""
    for tool in connection['tools']:
        original_tool_name = tool['name']  # e.g., "firecrawl_scrape"
        namespaced_tool_name = f"{namespace}__{original_tool_name}"  # e.g., "web__firecrawl_scrape"

        # Store tool mapping for delegation
        self.third_party_tools[namespaced_tool_name] = {
            'server_id': server_id,
            'original_name': original_tool_name,
            'schema': tool
        }

        # Create delegated tool function
        await self._create_delegated_tool(namespaced_tool_name, original_tool_name, server_id, tool)
```

### 4. Tool Delegation Logic

When a client calls a delegated tool, it's automatically routed to the correct server:

```python
async def delegated_tool_func(ctx: Context, **kwargs) -> str:
    """Delegated tool function that proxies to third-party server."""
    
    # 1. Handle argument unwrapping
    if 'kwargs' in kwargs and len(kwargs) == 1:
        tool_input = kwargs['kwargs']
    else:
        tool_input = kwargs

    # 2. Add tool-specific enhancements
    if original_name.startswith('firecrawl_'):
        if 'formats' not in tool_input:
            tool_input['formats'] = ['markdown']

    # 3. Delegate to third-party server
    result = await self._call_tool_with_retry(
        server_id=server_id,
        tool_name=original_name,  # Use original name
        tool_input=tool_input,
        tool_call_id=f"delegated_{namespaced_name}"
    )
    
    return result['content']
```

### 5. MultiMCPClient Implementation

The MultiMCPClient manages connections to multiple MCP servers:

```python
class MultiMCPClient:
    def __init__(self):
        self.connections: Dict[str, Dict[str, Any]] = {}  # Track all connections
        
    async def add_server(self, server_id: str, server_url: str, protocol: str = "http"):
        """Connect to an MCP server and store connection info."""
        
        # Create appropriate client (HTTP or SSE)
        if protocol.lower() == "http":
            client = MCPHTTPClientLib()
        elif protocol.lower() == "sse":
            client = MCPSSEClientLib()
            
        # Connect and store connection info
        success = await client.connect_to_server(server_url)
        if success:
            self.connections[server_id] = {
                'client': client,
                'url': server_url,
                'protocol': protocol,
                'tools': client.available_tools  # Cache available tools
            }
```

## 📊 Data Flow: Client Request to Response

```mermaid
sequenceDiagram
    participant Client as Chat Client
    participant Enhanced as Enhanced MCP Server
    participant Multi as MultiMCPClient
    participant Firecrawl as Firecrawl MCP Server

    Client->>Enhanced: call_tool("web__firecrawl_scrape", {"url": "..."})
    Enhanced->>Enhanced: Look up tool mapping
    Note over Enhanced: web__firecrawl_scrape → firecrawl_scrape on firecrawl-mcp
    Enhanced->>Enhanced: Unwrap arguments & add defaults
    Enhanced->>Multi: call_tool("firecrawl-mcp", "firecrawl_scrape", args)
    Multi->>Firecrawl: MCP tool call via SSE
    Firecrawl-->>Multi: Tool result
    Multi-->>Enhanced: Tool result
    Enhanced->>Enhanced: Process & format result
    Enhanced-->>Client: Final response
```

## 🔧 Key Components Explained

### Tool Name Mapping
```python
# Original tool from Firecrawl server
original_name = "firecrawl_scrape"

# Namespaced tool exposed to client
namespaced_name = "web__firecrawl_scrape"

# Mapping stored for delegation
self.third_party_tools["web__firecrawl_scrape"] = {
    'server_id': 'firecrawl-mcp',
    'original_name': 'firecrawl_scrape',
    'schema': tool_schema
}
```

### Argument Processing Pipeline
```python
# 1. Receive arguments from client
kwargs = {"url": "https://example.com"}

# 2. Unwrap if needed (fix double-wrapping)
if 'kwargs' in kwargs and len(kwargs) == 1:
    tool_input = kwargs['kwargs']
else:
    tool_input = kwargs

# 3. Add tool-specific defaults
if original_name.startswith('firecrawl_'):
    tool_input['formats'] = ['markdown']
    tool_input['onlyMainContent'] = True

# 4. Send to third-party server
result = await multi_client.call_tool(server_id, original_name, tool_input)
```

### Connection Management
```python
async def call_tool(self, server_id: str, tool_name: str, tool_input: Any) -> Dict[str, Any]:
    """Route tool call to the appropriate server."""
    
    if server_id not in self.connections:
        return {'success': False, 'error': f"Server {server_id} not connected"}
        
    # Get the specific client for this server
    client = self.connections[server_id]['client']
    
    # Call the tool on the third-party server
    result = await client.call_tool(tool_name, tool_input, tool_call_id)
    
    return {
        'success': result.success,
        'content': result.content,
        'error': result.error,
        'server_id': server_id,
        'tool_name': tool_name
    }
```

## 🎯 Benefits of This Architecture

### 1. Single Connection Point
- Client connects to **one server** (port 9000)
- Gets access to **multiple backend servers**
- Simplified client configuration

### 2. Tool Namespacing
- Prevents name conflicts: `firecrawl_scrape` vs `web__firecrawl_scrape`
- Clear distinction from local tools with single underscores
- Easy identification of third-party tools with double underscores

### 3. Transparent Delegation
- Client doesn't know tools are delegated
- Automatic argument processing
- Built-in retry logic and error handling

### 4. Configuration-Driven
- Easy to add new MCP servers via config
- Supports multiple protocols (HTTP, SSE, stdio)
- Environment variable management

### 5. Fault Tolerance
- Connection retry logic
- Graceful fallbacks
- Individual server failures don't affect others

## 🔄 Tool Call Flow Summary

1. **Client** calls `web__firecrawl_scrape` on Enhanced Server
2. **Enhanced Server** looks up tool mapping → `firecrawl_scrape` on `firecrawl-mcp`
3. **Enhanced Server** processes arguments (unwrap, add defaults)
4. **MultiMCPClient** routes call to appropriate server connection
5. **Firecrawl Server** executes the actual tool
6. **Response** flows back through the chain with proper formatting

This architecture allows you to have a **unified MCP interface** while leveraging **specialized tools from multiple providers**!

## 📁 Configuration

### Server Configuration (server_config.json)

The Enhanced MCP Server uses the Augment pattern for configuration:

```json
{
  "description": "Enhanced MCP Server Configuration (Augment Pattern)",
  "mcpServers": {
    "firecrawl-mcp": {
      "enabled": true,
      "command": "npx",
      "args": ["-y", "firecrawl-mcp"],
      "env": {
        "FIRECRAWL_API_KEY": "fc-your-api-key-here"
      },
      "namespace": "web",
      "description": "Firecrawl MCP server spawned via npx"
    },
    "filesystem-mcp": {
      "enabled": false,
      "command": "npx",
      "args": ["@modelcontextprotocol/server-filesystem", "/tmp"],
      "env": {},
      "namespace": "fs",
      "description": "Filesystem MCP server"
    }
  }
}
```

### Usage with chat_term

```bash
python -m gaia.gaia_ceto.ceto_v002.chat_term \
  --llm mcp-http \
  --mcp-http-server http://localhost:9000/mcp
```

## 🚀 Available Tools

### Local Tools (7)
- `echostring` - Basic echo functionality
- `echostring_table` - Table format echo
- `long_task` - Long-running task with progress
- `firecrawl_scrape_text_only` - Lightweight text scraping
- `server_status` - Server health monitoring
- `list_all_tools` - Tool inventory
- `third_party_health` - Third-party connection health

### Firecrawl Tools (8)
- `web__firecrawl_scrape` - Advanced web scraping
- `web__firecrawl_map` - Website URL discovery
- `web__firecrawl_crawl` - Multi-page crawling
- `web__firecrawl_check_crawl_status` - Crawl job monitoring
- `web__firecrawl_search` - Web search with content extraction
- `web__firecrawl_extract` - Structured data extraction
- `web__firecrawl_deep_research` - AI-powered research
- `web__firecrawl_generate_llmstxt` - LLM permission files

## 🛠️ Development

### Starting the Server

```bash
cd gaia/gaia_ceto/proto_mcp_http
python mcp_http_server_enhanced.py --port 9000 --config server_config.json
```

### Testing

```bash
python test_enhanced_server_status.py
```

### Adding New MCP Servers

1. Add server configuration to `server_config.json`
2. Implement connection logic in `_spawn_and_connect_server()`
3. The tool registration happens automatically

This multi-MCP architecture provides a powerful, scalable way to aggregate tools from multiple MCP servers while maintaining a simple client interface.
