#!/usr/bin/env python3
"""
Test script to verify the Firecrawl argument handling fix.
"""

import asyncio
import sys
import os

# Add the gaia path to import MCP client libraries
sys.path.append(os.path.join(os.path.dirname(__file__), 'gaia'))

try:
    from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClient<PERSON>ib
except ImportError as e:
    print(f"Error importing MCP client library: {e}")
    sys.exit(1)


async def test_firecrawl_fix():
    """Test the Firecrawl argument handling fix."""
    
    # Create client
    client = MCPClientLib(debug_callback=lambda level, msg, data=None: print(f"[{level.upper()}] {msg}"))
    
    try:
        # Connect to the enhanced server
        print("Connecting to enhanced MCP server at http://localhost:9000/mcp...")
        success = await client.connect_to_server("http://localhost:9000/mcp")
        
        if not success:
            print("❌ Failed to connect to enhanced MCP server")
            return
        
        print("✅ Connected successfully!")
        
        # Check for Firecrawl tools
        firecrawl_tools = [tool for tool in client.available_tools if 'firecrawl' in tool['name'].lower() and 'web_' in tool['name']]
        if not firecrawl_tools:
            print("❌ No web_firecrawl tools found")
            return
        
        print(f"🔥 Found {len(firecrawl_tools)} Firecrawl tools")
        
        # Test web_firecrawl_scrape with simple arguments
        print("\n🔍 Testing web_firecrawl_scrape with simple URL...")
        try:
            test_input = {"url": "https://example.com"}
            result = await client.call_tool("web_firecrawl_scrape", test_input, "test_simple")
            
            if result.success:
                print("✅ Simple Firecrawl test successful!")
                content_str = str(result.content)
                print(f"Content preview: {content_str[:200]}...")
            else:
                print(f"❌ Simple Firecrawl test failed: {result.error}")
                
        except Exception as e:
            print(f"❌ Simple Firecrawl test error: {e}")
        
        # Test web_firecrawl_scrape with more complex arguments
        print("\n🔍 Testing web_firecrawl_scrape with complex arguments...")
        try:
            test_input = {
                "url": "https://example.com",
                "formats": ["markdown"],
                "onlyMainContent": True
            }
            result = await client.call_tool("web_firecrawl_scrape", test_input, "test_complex")
            
            if result.success:
                print("✅ Complex Firecrawl test successful!")
                content_str = str(result.content)
                print(f"Content preview: {content_str[:200]}...")
            else:
                print(f"❌ Complex Firecrawl test failed: {result.error}")
                
        except Exception as e:
            print(f"❌ Complex Firecrawl test error: {e}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        try:
            await client.cleanup()
            print("\n🧹 Client cleanup completed")
        except Exception as e:
            print(f"⚠️  Cleanup warning: {e}")


if __name__ == "__main__":
    print("🚀 Testing Firecrawl Argument Fix")
    print("=" * 35)
    asyncio.run(test_firecrawl_fix())
